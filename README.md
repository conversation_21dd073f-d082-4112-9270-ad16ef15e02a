# Chat Bubble Widget Usage Guide

This document explains how to integrate the Chat Bubble Widget into any website.

## Overview

The Chat Bubble Widget is a React-based chat interface that can be embedded in any HTML page. It provides a floating chat bubble that expands into a full chat interface when clicked.

## Features

- Floating chat bubble that can be placed anywhere on the page
- Expandable chat interface with message history
- Dark/light mode toggle
- Resizable chat window
- Emoji picker and emoticon conversion
- Markdown support for rich text messages
- User feedback system with star ratings and comments
- Configurable API endpoint
- Logout functionality
- Persistent chat history and feedback using localStorage

## Integration

> **🚀 NEW: Self-Contained Build!** The widget now requires only 2 local files with zero external dependencies. See [SELF-CONTAINED-BUILD.md](./SELF-CONTAINED-BUILD.md) for details.

### Quick Start (Recommended)

1. Include the widget scripts in your HTML:

```html
<!-- Self-contained widget - no external dependencies needed! -->
<script src="./dist/chat-bubble-widget.umd.cjs"></script>
<script src="./dist/chat-bubble-loader.js"></script>
```

2. Add a container div where the widget will be mounted:

```html
<div id="chat-bubble-root" data-api-url="/your-api-endpoint"></div>
```

That's it! The widget will auto-mount with the configured API URL.

### Manual Configuration

For more control over the mounting process:

```html
<div id="my-chat-container"></div>

<script src="./dist/chat-bubble-widget.umd.cjs"></script>
<script src="./dist/chat-bubble-loader.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    window.ChatBubbleLoader.mount('my-chat-container', {
        apiUrl: '/your-api-endpoint'
    });
});
</script>
```

### Configuration Options

The `mount` function accepts the following parameters:

- `targetId` (required): The ID of the DOM element where the widget will be mounted
- `config` (optional): An object with the following properties:
  - `apiUrl`: The URL of the API endpoint for the chat service

Example with all options:

```html
<script>
  window.ChatBubbleWidget.mount('chat-container', {
    apiUrl: '/api/cc-lg'
  });
</script>
```

## API Integration

The widget expects the API endpoint to accept POST requests to `/threads/{threadId}/runs/wait` with the following JSON payload:

```json
{
  "header": {
    "ID": "uuid",
    "Application": "A7459F80-5761-4E53-9270-B1FFA5C91D4F",
    "Bank": "NBG",
    "AgentId": ""
  },
  "payload": {
    "UserMessage": "message text"
  }
}
```

The API should respond with a JSON object containing:

```json
{
  "payload": {
    "agentMessage": "response text"
  }
}
```

### Feedback API

The widget also supports sending feedback for chat threads. It sends POST requests to `/threads/{threadId}/feedback` with the following JSON payload:

```json
{
  "header": {
    "ID": "uuid",
    "Application": "A7459F80-5761-4E53-9270-B1FFA5C91D4F",
    "Bank": "NBG",
    "AgentId": ""
  },
  "payload": {
    "rating": 5,
    "comment": "Optional feedback comment"
  }
}
```

The API should respond with a success message:

```json
{
  "payload": {
    "success": true,
    "message": "Feedback submitted successfully"
  }
}
```

## Environment Configuration

The widget can be configured for different environments using environment variables:

1. Create appropriate .env file for your environment:

For development (`/.env`):
```env
VITE_API_URL=/api/cc-lg
```

For production (`/.env.production`):
```env
VITE_API_URL=https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg
```

2. Build the widget for your target environment:
```bash
# For development environment
npm run build -- --mode development

# For production environment
npm run build -- --mode production
```

## Building from Source

1. Clone the repository
2. Install dependencies:
```bash
npm install
```
3. Build the widget:
```bash
# Production build (recommended)
npm run build

# Development build
npm run build:dev
```
4. The built files will be in the `dist` directory:
   - `chat-bubble-widget.umd.cjs` - Main widget bundle (~726KB)
   - `chat-bubble-loader.js` - Minimal loader script (~1.6KB)
   - `chat-bubble-widget.css` - Styles (auto-loaded)

## Mock Server

A mock server is included to simulate the backend API for development and testing purposes.

### Setting up the Mock Server

1. Navigate to the mock-server directory:
```bash
cd mock-server
```

2. Install dependencies:
```bash
npm install
```

3. Start the server:
```bash
npm start
```

Or for development with auto-restart:
```bash
npm run dev
```

4. The mock server will be available at `http://localhost:3000/cc-lg`

### Using the Mock Server with the Widget

To use the mock server with the widget, set the API URL to point to the mock server:

```html
<script>
  window.ChatBubbleWidget.mount('chat-container', {
    apiUrl: 'http://localhost:3000/cc-lg'
  });
</script>
```

Or set the environment variable:
```env
VITE_API_URL=http://localhost:3000/cc-lg
```

For more details about the mock server, see the [Mock Server README](./mock-server/README.md).

## Customization

The widget can be customized by modifying the source code:

- `src/chat-bubble-component.jsx`: The main React component
- `src/chat-bubble-loader.js`: The loader script that handles mounting the widget
- `src/widget-entry.jsx`: The entry point for the widget

## Browser Compatibility

The widget is compatible with all modern browsers:

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Dependencies

The widget is now **completely self-contained** with all dependencies bundled:

- ✅ React 19.0.0 (bundled)
- ✅ ReactDOM 19.0.0 (bundled)
- ✅ All Langchain dependencies (bundled)
- ✅ Axios, Emoji Picker, React Markdown (bundled)
- ✅ Custom SVG icons (no Font Awesome dependency)
- ✅ Dynamic reCAPTCHA loading (only when needed)

**No external CDN dependencies required!**

## Troubleshooting

If the widget doesn't appear:

1. Check the browser console for errors
2. Ensure the script is loaded correctly
3. Verify that the target element exists in the DOM
4. Check that the API endpoint is accessible

## License

This project is licensed under the MIT License - see the LICENSE file for details.
