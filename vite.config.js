import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import EnvironmentPlugin from 'vite-plugin-environment';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), EnvironmentPlugin(['NODE_ENV'])],
  base: '/chatbot/',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    lib: {
      // Main entry point for the widget
      entry: 'src/widget-entry.jsx',
      name: 'ChatBubbleWidget',
      formats: ['umd'],
      fileName: 'chat-bubble-widget'
    },
    rollupOptions: {
      external: [], // Bundle everything - no external dependencies
      output: {
        // No globals needed since we're bundling everything
        inlineDynamicImports: true, // Inline all dynamic imports for a single file
        manualChunks: undefined // Disable code splitting for a single bundle
      }
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3002',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        secure: false
      }
    }
  }
})
