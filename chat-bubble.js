// Chat Bubble Widget Loader for CDN React/ReactDOM

// You must bundle ChatBubble as a UMD/global and include it before this script,
// or paste the ChatBubble code here and use window.React, window.ReactDOM.

(function () {
  // Check for React and ReactDOM globals
  if (!window.React || !window.ReactDOM) {
    console.error("React and ReactDOM must be loaded as global scripts before chat-bubble.js");
    return;
  }

  // You must provide ChatBubble as a global, e.g. window.ChatBubble
  if (!window.ChatBubble) {
    console.error("ChatBubble component must be available as window.ChatBubble");
    return;
  }

  window.mountChatBubble = function (targetDivId) {
    var target = document.getElementById(targetDivId);
    if (!target) {
      console.error("ChatBubble target div not found:", targetDivId);
      return;
    }
    var ChatBubbleComponent = window.ChatBubble && window.ChatBubble.default ? window.ChatBubble.default : window.ChatBubble;
    window.ReactDOM.createRoot(target).render(window.React.createElement(ChatBubbleComponent));
  };
})();