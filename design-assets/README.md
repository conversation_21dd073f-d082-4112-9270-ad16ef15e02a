# NBG Chat Widget Design Assets

This directory contains all the design assets and specifications extracted from the Figma design file for the NBG (National Bank of Greece) Chat Widget.

## 📁 Directory Structure

```
design-assets/
├── README.md                           # This file
├── NBG-Chat-Design-Specification.md    # Complete design specification
├── nbg-design-tokens.css              # CSS custom properties/variables
├── component-breakdown.json           # Detailed component structure
├── icons/                             # Icon assets
│   ├── arrow-right-icon.png
│   └── attachment-icon.png
└── [22 component images]              # Individual component screenshots
```

## 🎨 Design Files

### Main Specification
- **NBG-Chat-Design-Specification.md**: Complete design specification with colors, typography, layout, and implementation notes
- **nbg-design-tokens.css**: CSS custom properties for all design tokens (colors, spacing, typography, etc.)
- **component-breakdown.json**: Detailed JSON structure of all Figma components with exact specifications

### Visual Assets
- **nbg-chat-bot-full.png**: Complete chat interface
- **nbg-header.png**: Header section
- **nbg-main-area.png**: Main chat area
- **nbg-footer.png**: Footer/input section
- **nbg-profile-icon.png**: Eva profile icon
- **nbg-chat-title.png**: NBG Chat title
- **nbg-close-icon.png**: Close button icon
- **nbg-send-icon.png**: Send button icon
- **nbg-attachment-icon.png**: Attachment icon
- And more component-specific images...

## 🎯 Key Design Elements

### Brand Colors
- **Primary**: `#003841` (NBG Dark Teal)
- **Secondary**: `#007B85` (NBG Light Teal)
- **Background**: `#F5F8F6` (Light green-gray)

### Typography
- **Font**: Aeonik Pro (NBG brand font)
- **Sizes**: 18px (headers), 14px (body), 12px (small text)
- **Weights**: 400 (regular), 700 (bold)

### Layout
- **Container**: 408px × 600px
- **Border Radius**: 12px
- **Shadow**: `0px 6px 15px 5px rgba(0, 56, 65, 0.1)`

## 🔧 Implementation Guide

### 1. CSS Variables
Import the design tokens:
```css
@import './design-assets/nbg-design-tokens.css';
```

### 2. Component Structure
Reference the `component-breakdown.json` for exact component specifications including:
- Dimensions and positioning
- Color values
- Typography settings
- Layout properties
- Interactive states

### 3. Greek Text Content
The design includes Greek text content:
- **Greeting**: "Καλημέρα!" (Good morning!)
- **Bot Response**: "Είμαι ο ψηφιακός βοηθός σας. Σχετικά με τι θέλετε να ενημερωθείτε;"
- **Input Placeholder**: "Γράψτε ένα μικρό και απλό μήνυμα"

### 4. Icons and Graphics
- Use the provided icon assets or implement as SVG
- Hero graphics in header have 40% opacity
- Profile icons use specific stroke weights (0.8px for large, 0.48px for small)

## 📱 Responsive Considerations

The design is fixed-width (408px) but should be adapted for:
- Mobile devices (full-width)
- Different screen densities
- Accessibility requirements

## 🎨 Component Variants

### Chat Bubbles
- **Bot messages**: White background, left-aligned with profile icon
- **User suggestions**: White background with teal border, right-aligned

### Profile Icons
- **Large** (40px): Header area
- **Small** (24px): Chat messages

### Interactive States
- Hover effects on buttons and suggestions
- Focus states for accessibility
- Loading states for messages

## 🔍 Figma Reference

- **File**: NBG - Live Chat
- **Node ID**: 1-4094
- **Last Modified**: 2025-05-23T11:28:40Z
- **Figma URL**: https://www.figma.com/design/9FvXgJQaWIioDKX7FCBOK8/NBG---Live-Chat?node-id=1-4094

## 📋 Implementation Checklist

- [ ] Import CSS design tokens
- [ ] Implement header with NBG branding
- [ ] Create chat message components
- [ ] Add suggestion button components
- [ ] Implement input field with Greek placeholder
- [ ] Add attachment functionality
- [ ] Implement profile icon component
- [ ] Add hover and focus states
- [ ] Test with Greek text content
- [ ] Ensure accessibility compliance
- [ ] Test responsive behavior

## 🚀 Next Steps

1. Review the complete design specification
2. Import the CSS design tokens into your project
3. Use the component breakdown for exact implementation details
4. Reference the visual assets for pixel-perfect implementation
5. Test with the provided Greek text content

For any questions about the design implementation, refer to the detailed specification files in this directory.
