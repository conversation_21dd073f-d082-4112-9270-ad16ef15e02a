/* NBG Chat Widget Design Tokens */
/* Based on Figma design: NBG - Live Chat */

:root {
  /* === COLORS === */
  
  /* Primary Brand Colors */
  --nbg-primary-dark: #003841;      /* Main NBG brand color */
  --nbg-primary-light: #007B85;     /* Secondary/accent color */
  --nbg-primary-medium: #00626A;    /* Graphics/decorative elements */
  
  /* Background Colors */
  --nbg-bg-main: #F5F8F6;          /* Main chat area background */
  --nbg-bg-white: #FFFFFF;         /* Cards, bubbles, input fields */
  --nbg-bg-profile: #D5EFF4;       /* Profile icon background */
  --nbg-bg-input: #F8F9FA;         /* Input field background */
  
  /* Text Colors */
  --nbg-text-primary: #212121;     /* Primary text color */
  --nbg-text-secondary: #6A6C6A;   /* Secondary text, placeholders */
  --nbg-text-muted: #949794;       /* Light gray for icons/borders */
  
  /* Border Colors */
  --nbg-border-light: #E1E5E9;     /* Light borders */
  --nbg-border-accent: #007B85;    /* Accent borders */
  
  /* === TYPOGRAPHY === */
  
  /* Font Families */
  --nbg-font-primary: 'Aeonik Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  
  /* Font Sizes */
  --nbg-font-size-xl: 18px;        /* Header title */
  --nbg-font-size-lg: 14px;        /* Message text, suggestions */
  --nbg-font-size-md: 12px;        /* Small text, attachment labels */
  
  /* Font Weights */
  --nbg-font-weight-regular: 400;
  --nbg-font-weight-bold: 700;
  
  /* Line Heights */
  --nbg-line-height-normal: 1.5em;
  --nbg-line-height-compact: 1.3em;
  
  /* === SPACING === */
  
  /* Padding Values */
  --nbg-padding-xs: 4px;
  --nbg-padding-sm: 8px;
  --nbg-padding-md: 12px;
  --nbg-padding-lg: 16px;
  --nbg-padding-xl: 20px;
  --nbg-padding-xxl: 24px;
  --nbg-padding-xxxl: 32px;
  
  /* Margin Values */
  --nbg-margin-xs: 4px;
  --nbg-margin-sm: 8px;
  --nbg-margin-md: 14px;
  --nbg-margin-lg: 16px;
  
  /* Gap Values */
  --nbg-gap-xs: 4px;
  --nbg-gap-sm: 8px;
  --nbg-gap-md: 12px;
  --nbg-gap-lg: 16px;
  
  /* === DIMENSIONS === */
  
  /* Container Sizes */
  --nbg-container-width: 408px;
  --nbg-container-height: 600px;
  
  /* Icon Sizes */
  --nbg-icon-sm: 16px;
  --nbg-icon-md: 24px;
  --nbg-icon-lg: 40px;
  
  /* Profile Icon Sizes */
  --nbg-profile-sm: 24px;          /* Chat area profile icon */
  --nbg-profile-lg: 40px;          /* Header profile icon */
  
  /* === BORDER RADIUS === */
  
  --nbg-radius-sm: 4px;
  --nbg-radius-md: 8px;
  --nbg-radius-lg: 12px;
  --nbg-radius-xl: 18px;
  --nbg-radius-xxl: 24px;
  --nbg-radius-circle: 50%;
  
  /* Chat Bubble Specific Radius */
  --nbg-bubble-radius-top: 8px 8px 8px 0px;
  --nbg-bubble-radius-middle: 2px 8px 8px 2px;
  --nbg-bubble-radius-bottom: 8px 8px 0px 8px;
  
  /* === SHADOWS === */
  
  --nbg-shadow-main: 0px 6px 15px 5px rgba(0, 56, 65, 0.1);
  --nbg-shadow-hover: 0px 8px 20px 8px rgba(0, 56, 65, 0.15);
  
  /* === BORDERS === */
  
  --nbg-border-width-thin: 1px;
  --nbg-border-width-medium: 1.33px;
  --nbg-border-width-thick: 1.5px;
  
  /* Stroke Weights for Icons */
  --nbg-stroke-thin: 0.48px;       /* Small profile icons */
  --nbg-stroke-medium: 0.8px;      /* Large profile icons */
  --nbg-stroke-thick: 1.33px;      /* UI icons */
  --nbg-stroke-heavy: 1.5px;       /* Send button icon */
  
  /* === OPACITY === */
  
  --nbg-opacity-graphics: 0.4;     /* Hero graphics opacity */
  --nbg-opacity-disabled: 0.5;
  --nbg-opacity-hover: 0.8;
  
  /* === Z-INDEX === */
  
  --nbg-z-base: 1;
  --nbg-z-overlay: 10;
  --nbg-z-modal: 100;
  --nbg-z-tooltip: 1000;
  
  /* === TRANSITIONS === */
  
  --nbg-transition-fast: 0.15s ease;
  --nbg-transition-normal: 0.2s ease;
  --nbg-transition-slow: 0.3s ease;
  
  /* === LAYOUT SPECIFIC === */
  
  /* Header Layout */
  --nbg-header-padding: var(--nbg-padding-lg) var(--nbg-padding-xxxl) var(--nbg-padding-lg) var(--nbg-padding-lg);
  
  /* Chat Area Layout */
  --nbg-chat-padding: var(--nbg-padding-sm) var(--nbg-padding-lg);
  --nbg-chat-gap: var(--nbg-gap-sm);
  
  /* Footer Layout */
  --nbg-footer-padding: var(--nbg-padding-lg) var(--nbg-padding-xxl);
  --nbg-footer-gap: var(--nbg-margin-md);
  
  /* Message Bubble Layout */
  --nbg-bubble-padding: var(--nbg-padding-sm) var(--nbg-padding-lg);
  --nbg-bubble-gap: var(--nbg-gap-xs);
  
  /* Input Field Layout */
  --nbg-input-padding: var(--nbg-padding-md) var(--nbg-padding-lg);
  --nbg-input-border-radius: var(--nbg-radius-xxl);
  
  /* Suggestion Button Layout */
  --nbg-suggestion-padding: var(--nbg-padding-sm) var(--nbg-padding-lg);
  --nbg-suggestion-gap: var(--nbg-gap-lg);
}

/* === COMPONENT SPECIFIC TOKENS === */

/* Profile Icon Variants */
.nbg-profile-icon {
  --size: var(--nbg-profile-lg);
  --background: var(--nbg-bg-profile);
  --stroke-width: var(--nbg-stroke-medium);
  --stroke-color: var(--nbg-primary-dark);
}

.nbg-profile-icon--small {
  --size: var(--nbg-profile-sm);
  --stroke-width: var(--nbg-stroke-thin);
}

/* Chat Bubble Variants */
.nbg-chat-bubble {
  --background: var(--nbg-bg-white);
  --text-color: var(--nbg-text-primary);
  --border-radius: var(--nbg-bubble-radius-middle);
  --padding: var(--nbg-bubble-padding);
}

.nbg-chat-bubble--top {
  --border-radius: var(--nbg-bubble-radius-top);
}

.nbg-chat-bubble--bottom {
  --border-radius: var(--nbg-bubble-radius-bottom);
}

/* Suggestion Button */
.nbg-suggestion-button {
  --background: var(--nbg-bg-white);
  --border-color: var(--nbg-border-accent);
  --text-color: var(--nbg-primary-dark);
  --border-radius: var(--nbg-radius-md);
  --padding: var(--nbg-suggestion-padding);
}

/* Input Field */
.nbg-input-field {
  --background: var(--nbg-bg-input);
  --border-color: var(--nbg-border-light);
  --text-color: var(--nbg-text-primary);
  --placeholder-color: var(--nbg-text-secondary);
  --border-radius: var(--nbg-input-border-radius);
  --padding: var(--nbg-input-padding);
}

.nbg-input-field:focus {
  --border-color: var(--nbg-border-accent);
}

/* Greek Text Content */
.nbg-text-content {
  --greeting: "Καλημέρα!";
  --bot-response: "Είμαι ο ψηφιακός βοηθός σας. Σχετικά με τι θέλετε να ενημερωθείτε;";
  --input-placeholder: "Γράψτε ένα μικρό και απλό μήνυμα";
  --attachment-label: "Attachment";
}
