# NBG Chat Widget Design Specification

## Overview
This document contains the complete design specification for the NBG (National Bank of Greece) Chat Widget based on the Figma design file.

## Design Assets
All design assets have been downloaded to the `design-assets/` directory:
- Main interface screenshots
- Individual component images
- Icons and graphics

## Dimensions
- **Container Width**: 408px
- **Container Height**: 600px
- **Border Radius**: 12px

## Color Palette

### Primary Colors
- **NBG Dark Teal**: `#003841` (Primary brand color)
- **NBG Light Teal**: `#007B85` (Secondary/accent color)
- **NBG Teal Variant**: `#00626A` (Graphics/decorative elements)

### Background Colors
- **Main Background**: `#F5F8F6` (Light green-gray)
- **White**: `#FFFFFF` (Cards, bubbles, input fields)
- **Chat Bubble Background**: `#D5EFF4` (Light blue-gray for profile icons)

### Text Colors
- **Primary Text**: `#212121` (Dark gray)
- **Secondary Text**: `#6A6C6A` (Medium gray for placeholders)
- **Muted Text**: `#949794` (Light gray for icons/borders)

## Typography

### Font Family
- **Primary**: `Aeonik Pro` (NBG brand font)
- **Fallback**: `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif`

### Font Weights & Sizes
- **Header Title**: 18px, 700 (Bold)
- **Message Text**: 14px, 400 (Regular)
- **Suggestion Text**: 14px, 700 (Bold)
- **Input Placeholder**: 14px, 400 (Regular)
- **Attachment Text**: 12px, 400 (Regular)

### Line Heights
- **Standard**: 1.5em
- **Compact**: 1.2999999523162842em (for small text)

## Layout Structure

### Header Section
- **Background**: `#003841` (NBG Dark Teal)
- **Padding**: 16px 32px 16px 16px
- **Layout**: Row with space-between alignment
- **Elements**:
  - Hero graphics (decorative patterns with 40% opacity)
  - Profile icon (40px × 40px circle)
  - "NBG Chat" title
  - Control buttons (minimize line + close icon)

### Main Chat Area
- **Background**: `#F5F8F6`
- **Padding**: 8px 16px (top/bottom), 0px (sides for full-width bubbles)
- **Layout**: Column with 8px gap between messages

### Footer/Input Section
- **Background**: `#FFFFFF`
- **Padding**: 16px 24px
- **Border Top**: 1px solid (light gray)
- **Layout**: Column with 14px gap

## Component Specifications

### Profile Icon (Eva)
- **Size**: 40px × 40px (header), 24px × 24px (chat)
- **Background**: `#D5EFF4`
- **Border Radius**: 50% (circle)
- **Icon**: Custom Eva avatar with `#003841` strokes (0.8px weight for large, 0.48px for small)

### Chat Bubbles

#### Bot Messages
- **Background**: `#FFFFFF`
- **Border Radius**: 8px 8px 8px 0px (top), 2px 8px 8px 2px (middle)
- **Padding**: 8px 16px
- **Text Color**: `#212121`
- **Max Width**: Flexible based on content
- **Alignment**: Left-aligned with profile icon

#### User Suggestion Buttons
- **Background**: `#FFFFFF`
- **Border**: 1px solid `#007B85`
- **Border Radius**: 8px
- **Padding**: 8px 16px
- **Layout**: Row with space-between, 16px gap
- **Text**: 14px, 700 weight, `#003841` color
- **Icon**: Arrow right (16px × 16px)
- **Alignment**: Right-aligned (flex-end)

### Input Field
- **Background**: `#F8F9FA` (light gray)
- **Border**: 1px solid `#E1E5E9`
- **Border Radius**: 24px
- **Padding**: 12px 16px
- **Placeholder**: "Γράψτε ένα μικρό και απλό μήνυμα" (Greek text)
- **Placeholder Color**: `#6A6C6A`

### Buttons & Icons

#### Send Button
- **Background**: Transparent
- **Icon Color**: `#949794`
- **Stroke Weight**: 1.5px
- **Size**: 24px × 24px

#### Attachment Button
- **Icon Color**: `#007B85`
- **Stroke Weight**: 1.33px
- **Size**: 16px × 16px
- **Text**: "Attachment" (12px, `#007B85`)

#### Close Button
- **Background**: `#FFFFFF` rectangle (16px × 1.71px)
- **Icon**: Close/X symbol (16px × 16px)
- **Color**: `#FFFFFF`

## Shadows & Effects
- **Main Container**: `0px 6px 15px 5px rgba(0, 56, 65, 0.1)`
- **Hover States**: Subtle color transitions and shadow increases

## Interactive States

### Hover Effects
- **Suggestion Buttons**: Border color intensifies, subtle shadow
- **Input Field**: Border color changes to `#007B85`
- **Attachment Button**: Background color change

### Focus States
- **Input Field**: Border color `#007B85`
- **Buttons**: Outline with brand colors

## Text Content (Greek)

### Messages
- **Bot Greeting**: "Καλημέρα!" (Good morning!)
- **Bot Response**: "Είμαι ο ψηφιακός βοηθός σας. Σχετικά με τι θέλετε να ενημερωθείτε;" (I am your digital assistant. What would you like to be informed about?)

### UI Elements
- **Input Placeholder**: "Γράψτε ένα μικρό και απλό μήνυμα" (Write a short and simple message)
- **Attachment Label**: "Attachment"
- **Suggestion Buttons**: "Lorem ipsum" (placeholder text in design)

## Responsive Considerations
- Fixed width design (408px)
- Scalable for different screen sizes
- Maintain aspect ratios for icons and graphics

## Implementation Notes
- Use CSS Grid/Flexbox for layout
- Implement smooth transitions for interactive states
- Ensure accessibility with proper ARIA labels
- Support for Greek text rendering
- Consider loading states for messages
- Implement proper focus management for keyboard navigation
