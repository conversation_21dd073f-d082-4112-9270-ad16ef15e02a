{"figmaData": {"fileKey": "9FvXgJQaWIioDKX7FCBOK8", "fileName": "NBG - Live Chat", "nodeId": "1-4094", "lastModified": "2025-05-23T11:28:40Z"}, "mainContainer": {"id": "1:4094", "name": "Chat bot", "type": "FRAME", "dimensions": {"width": 408, "height": 600}, "borderRadius": "12px", "boxShadow": "0px 6px 15px 5px rgba(0, 56, 65, 0.1)", "layout": "column"}, "header": {"id": "1:4095", "name": "Header", "background": "#003841", "borderRadius": "4px 4px 0px 0px", "padding": "16px 32px 16px 16px", "layout": "row, space-between, center aligned", "components": {"heroGraphics1": {"id": "1:4096", "opacity": 0.4, "position": "absolute", "location": {"x": 52, "y": -208.92}, "dimensions": {"width": 788, "height": 328.33}}, "heroGraphics2": {"id": "1:4097", "opacity": 0.4, "position": "absolute", "location": {"x": -378, "y": -87.92}, "dimensions": {"width": 788, "height": 328.33}}, "headerFrame": {"id": "1:4098", "layout": "row, center aligned, 8px gap", "components": {"profileIcon": {"id": "1:4099", "componentId": "1:270", "variant": "Person=Eva", "dimensions": {"width": 40, "height": 40}, "background": "#D5EFF4", "borderRadius": "50%", "strokeColor": "#003841", "strokeWeight": "0.8px"}, "title": {"id": "1:4100", "text": "NBG Chat", "fontSize": 18, "fontWeight": 700, "fontFamily": "Aeonik Pro", "color": "#FFFFFF", "lineHeight": "1.5em"}}}, "headerControls": {"id": "1:4101", "layout": "row, center aligned, 32px gap", "components": {"minimizeButton": {"id": "1:4102", "type": "RECTANGLE", "dimensions": {"width": 16, "height": 1.71}, "background": "#FFFFFF"}, "closeIcon": {"id": "1:4103", "type": "IMAGE-SVG", "dimensions": {"width": 16, "height": 16}, "color": "#FFFFFF"}}}}}, "mainArea": {"id": "1:4104", "name": "Main Area", "background": "#F5F8F6", "layout": "column, center aligned, 8px gap", "padding": "0px 0px 8px", "components": {"botChatUser": {"id": "1:4105", "componentId": "1:383", "variant": "Who is this?=<PERSON><PERSON>", "showProfileIcon": true, "layout": "column, center aligned, 8px gap", "padding": "8px 16px", "components": {"chatBubblesWithIcon": {"layout": "row, flex-end aligned, 8px gap", "profileIcon": {"componentId": "1:270", "variant": "Person=Eva", "dimensions": {"width": 24, "height": 24}, "background": "#FFFFFF", "strokeColor": "#003841", "strokeWeight": "0.48px"}, "bubbles": {"layout": "column, 4px gap", "bubble1": {"componentId": "1:356", "variant": "Type=Chat, Who is this?=Eve, Position=Top, Description=True", "background": "#FFFFFF", "borderRadius": "8px 8px 8px 0px", "padding": "8px 16px", "text": "Καλημέρα!", "fontSize": 14, "fontWeight": 400, "color": "#212121", "lineHeight": "1.5em"}, "bubble2": {"componentId": "1:358", "variant": "Type=<PERSON><PERSON>, Who is this?=Eve, Position=Default, Description=True", "background": "#FFFFFF", "borderRadius": "2px 8px 8px 2px", "padding": "8px 16px", "text": "Είμαι ο ψηφια<PERSON><PERSON>ς βοηθός σας. Σχετικά με τι θέλετε να ενημερωθείτε;", "fontSize": 14, "fontWeight": 400, "color": "#212121", "lineHeight": "1.5em"}}}}}, "userSuggestions": {"id": "1:4106", "componentId": "1:377", "variant": "Who is this?=User", "showProfileIcon": false, "layout": "column, center aligned, flex-end, 8px gap", "padding": "8px 16px", "components": {"bubbles": {"layout": "column, center aligned, flex-end, 4px gap", "suggestions": [{"componentId": "1:373", "variant": "Type=Suggestion, Who is this?=Eve, Position=Default, Description=False", "background": "#FFFFFF", "border": "1px solid #007B85", "borderRadius": "8px", "padding": "8px 16px", "layout": "row, center aligned, 16px gap", "title": {"text": "Lorem ipsum", "fontSize": 14, "fontWeight": 700, "color": "#003841", "lineHeight": "1.5em"}, "icon": {"componentId": "1:351", "name": "icon / 24px / directions / straight arrows / long right", "dimensions": {"width": 16, "height": 16}, "strokeColor": "#003841", "strokeWeight": "1.33px"}}]}}}}}, "footer": {"id": "1:4107", "name": "Footer", "background": "#FFFFFF", "boxShadow": "0px 6px 15px 5px rgba(0, 56, 65, 0.1)", "dimensions": {"width": 408}, "padding": "16px 24px", "layout": "row, stretch aligned, 8px gap", "components": {"insideFooterArea": {"id": "1:4108", "layout": "column, center aligned, 14px gap", "components": {"inputArea": {"id": "1:4109", "layout": "row, center aligned, 16px gap", "components": {"userInput": {"id": "1:4110", "type": "TEXT", "text": "Γράψτε ένα μικρό και απλό μήνυμα", "fontSize": 14, "fontWeight": 400, "color": "#6A6C6A", "lineHeight": "1.5em"}, "sendIcon": {"id": "1:4111", "type": "FRAME", "background": "#FFFFFF", "dimensions": {"width": 24, "height": 24}, "components": {"vectors": [{"strokeColor": "#949794", "strokeWeight": "1.5px"}]}}}}, "functionalities": {"id": "1:4114", "layout": "row, 12px gap", "components": {"attachment": {"id": "1:4115", "layout": "row, center aligned, 4px gap", "components": {"attachmentIcon": {"id": "1:4116", "componentId": "1:391", "dimensions": {"width": 16, "height": 16}, "strokeColor": "#007B85", "strokeWeight": "1.33px"}, "attachmentText": {"id": "1:4117", "text": "Attachment", "fontSize": 12, "fontWeight": 400, "color": "#007B85", "lineHeight": "1.3em"}}}}}}}}}, "textContent": {"greek": {"greeting": "Καλημέρα!", "botResponse": "Είμαι ο ψηφια<PERSON><PERSON>ς βοηθός σας. Σχετικά με τι θέλετε να ενημερωθείτε;", "inputPlaceholder": "Γράψτε ένα μικρό και απλό μήνυμα"}, "english": {"attachmentLabel": "Attachment", "suggestionPlaceholder": "Lorem ipsum"}}, "componentLibrary": {"profileIcon": {"componentSetId": "1:269", "variants": {"eva": {"componentId": "1:270", "name": "Person=Eva"}}}, "chatBubble": {"componentSetId": "1:355", "variants": {"chatEveTop": {"componentId": "1:356", "name": "Type=Chat, Who is this?=Eve, Position=Top, Description=True"}, "chatEveDefault": {"componentId": "1:358", "name": "Type=<PERSON><PERSON>, Who is this?=Eve, Position=Default, Description=True"}, "chatEveBottom": {"componentId": "1:360", "name": "Type=Chat, Who is this?=Eve, Position=Bottom, Description=True"}, "suggestionEve": {"componentId": "1:373", "name": "Type=Suggestion, Who is this?=Eve, Position=Default, Description=False"}}}, "chatUser": {"componentSetId": "1:376", "variants": {"chatBot": {"componentId": "1:383", "name": "Who is this?=<PERSON><PERSON>"}, "user": {"componentId": "1:377", "name": "Who is this?=User"}}}, "icons": {"arrowRight": {"componentId": "1:351", "name": "icon / 24px / directions / straight arrows / long right"}, "attachment": {"componentId": "1:391", "name": "icon / 24px / editor / other / attachment"}}, "heroGraphics": {"componentSetId": "1:618", "variant": {"componentId": "1:727", "name": "Number=5, Sector=Individuals / Group"}}}}