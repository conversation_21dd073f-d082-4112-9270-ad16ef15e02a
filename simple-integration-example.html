<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Chat Widget Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .integration-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Simple Chat Widget Integration</h1>

        <div class="integration-info">
            <h3>✅ Self-Contained Widget</h3>
            <p>This page demonstrates the chat widget with <strong>zero external dependencies</strong>!</p>
            <p>Only requires:</p>
            <ul>
                <li><code>dist/chat-bubble-widget.umd.cjs</code> - The main widget bundle</li>
                <li><code>dist/chat-bubble-loader.js</code> - The minimal loader script</li>
            </ul>
        </div>

        <h2>📋 Integration Steps</h2>
        <ol>
            <li>Include the two script files in your HTML</li>
            <li>Add a container div with id <code>chat-bubble-root</code></li>
            <li>Optionally configure with data attributes</li>
        </ol>

        <h3>Basic HTML:</h3>
        <pre><code>&lt;!-- Container for the chat widget --&gt;
&lt;div id="chat-bubble-root" data-api-url="/your-api-endpoint"&gt;&lt;/div&gt;

&lt;!-- Load the widget scripts --&gt;
&lt;script src="dist/chat-bubble-widget.umd.cjs"&gt;&lt;/script&gt;
&lt;script src="dist/chat-bubble-loader.js"&gt;&lt;/script&gt;</code></pre>

        <h3>Manual Configuration:</h3>
        <pre><code>&lt;script&gt;
// Wait for the loader to be available
document.addEventListener('DOMContentLoaded', function() {
    if (window.ChatBubbleLoader) {
        window.ChatBubbleLoader.mount('your-container-id', {
            apiUrl: '/your-api-endpoint'
        });
    }
});
&lt;/script&gt;</code></pre>

        <p><strong>Features:</strong></p>
        <ul>
            <li>✅ No Font Awesome CDN dependency (uses inline SVG icons)</li>
            <li>✅ reCAPTCHA loads dynamically only when needed</li>
            <li>✅ All React dependencies bundled</li>
            <li>✅ All other dependencies bundled</li>
            <li>✅ Single UMD file + minimal loader</li>
        </ul>
    </div>

    <!-- The actual chat widget integration -->
    <div id="chat-bubble-root" data-api-url="/api/cc-lg"></div>

    <!-- Load the self-contained chat bubble widget -->
    <script src="./dist/chat-bubble-widget.umd.cjs"></script>
    <script src="./dist/chat-bubble-loader.js"></script>
</body>
</html>
