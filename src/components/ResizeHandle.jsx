import { useRef } from 'react';
import theme from '../styles/theme';

/**
 * Resize handle component for the chat window
 * @param {Object} props - Component props
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {Function} props.onResizeStart - Handler for resize start
 * @returns {JSX.Element} - Rendered component
 */
const ResizeHandle = ({ darkMode, onResizeStart }) => {
  // Resize handle style
  const resizeHandleStyle = {
    position: 'absolute',
    left: '0',
    top: '0',
    width: '12px', // Wider to make it easier to grab
    height: '100%',
    cursor: 'ew-resize',
    zIndex: theme.zIndex.above,
    backgroundColor: darkMode 
      ? 'rgba(255, 255, 255, 0.05)' 
      : 'rgba(0, 0, 0, 0.02)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    touchAction: 'none' // Prevent default touch actions for better mobile support
  };

  // Dot style for the resize handle
  const dotStyle = {
    width: '4px',
    height: '4px',
    backgroundColor: darkMode 
      ? 'rgba(255, 255, 255, 0.3)' 
      : 'rgba(0, 0, 0, 0.2)',
    borderRadius: theme.borderRadius.circle,
  };

  return (
    <div
      style={resizeHandleStyle}
      onMouseDown={onResizeStart}
      onTouchStart={onResizeStart}
      title="Drag to resize"
      className="resize-handle"
    >
      {/* Three dots to indicate draggable area */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
        <div style={dotStyle} />
        <div style={dotStyle} />
        <div style={dotStyle} />
      </div>
    </div>
  );
};

export default ResizeHandle;
