import { useState, useEffect } from 'react';
import theme from '../styles/theme';

// Import satisfaction icons
import satisfactionIcon1 from '../../design-assets/icons/Satisfaction icons 1.svg';
import satisfactionIcon2 from '../../design-assets/icons/Satisfaction icons 2.svg';
import satisfactionIcon3 from '../../design-assets/icons/Satisfaction icons 3.svg';
import satisfactionIcon4 from '../../design-assets/icons/Satisfaction icons 4.svg';
import satisfactionIcon5 from '../../design-assets/icons/Satisfaction icons 5.svg';

// Import NBG check icon
import nbgCheckIcon from '../../design-assets/nbg-check-icon.svg';

/**
 * Feedback widget component
 * @param {Object} props - Component props
 * @param {string} props.threadId - Current thread ID
 * @param {Function} props.onSubmitFeedback - Function to handle feedback submission
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {Object} props.feedbackData - Existing feedback data for this thread
 * @param {Function} props.onThankYouShown - Function to mark thank you message as shown
 * @returns {JSX.Element|null} - Rendered component or null if no active thread
 */
const FeedbackWidget = ({
  threadId,
  onSubmitFeedback,
  darkMode,
  feedbackData,
  onThankYouShown
}) => {
  // State for feedback form
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // Satisfaction icons for ratings (1=very sad, 5=very happy)
  const satisfactionIcons = [
    satisfactionIcon1,
    satisfactionIcon2,
    satisfactionIcon3,
    satisfactionIcon4,
    satisfactionIcon5
  ];

  // State for managing thank you message visibility
  const [showThankYou, setShowThankYou] = useState(true);
  const [fadeOut, setFadeOut] = useState(false);

  // Don't render if there's no thread ID
  if (!threadId) return null;

  // Check if feedback was already submitted for this thread
  const feedbackSubmitted = feedbackData && threadId && feedbackData[threadId];

  // Check if thank you message was already shown for this thread
  const thankYouAlreadyShown = feedbackSubmitted && feedbackSubmitted.thankYouShown;

  // Effect to handle the fade-out and hiding of thank you message
  useEffect(() => {
    if (feedbackSubmitted && showThankYou && !thankYouAlreadyShown) {
      // Start fade-out after 2 seconds
      const fadeTimer = setTimeout(() => {
        setFadeOut(true);
      }, 2000);

      // Hide completely after 3 seconds (2s delay + 1s for animation)
      const hideTimer = setTimeout(() => {
        setShowThankYou(false);
        // Mark thank you as shown in storage
        if (onThankYouShown) {
          onThankYouShown(threadId);
        }
      }, 3000);

      // Clean up timers
      return () => {
        clearTimeout(fadeTimer);
        clearTimeout(hideTimer);
      };
    }
  }, [feedbackSubmitted, showThankYou, thankYouAlreadyShown, threadId, onThankYouShown]);

  // Container style
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '8px 0',
    borderTop: darkMode
      ? `1px solid ${theme.colors.dark.border}`
      : `1px solid ${theme.colors.light.border}`,
    backgroundColor: darkMode
      ? theme.colors.dark.surface
      : theme.colors.light.surface,
    marginTop: 'auto'
  };

  // Feedback button style
  const feedbackButtonStyle = {
    background: 'transparent',
    border: 'none',
    color: darkMode ? theme.colors.primary.light : theme.colors.primary.main,
    fontSize: theme.typography.fontSize.sm,
    cursor: 'pointer',
    padding: '4px 12px',
    borderRadius: theme.borderRadius.small,
    fontWeight: theme.typography.fontWeight.medium,
    transition: theme.transitions.fast,
    textDecoration: 'none',
    marginBottom: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '4px'
  };

  // Icon container style
  const iconContainerStyle = {
    display: 'flex',
    justifyContent: 'center',
    gap: '8px',
    marginBottom: '12px'
  };

  // Icon wrapper style with white rectangle background
  const getIconWrapperStyle = (index) => {
    const isSelected = rating === index;
    const isHovered = hoveredRating === index;

    return {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '40px',
      height: '40px',
      backgroundColor: isSelected ? '#e0e0e0' : '#ffffff', // Gray when selected, white otherwise
      borderRadius: '6px', // Low radius as requested
      cursor: 'pointer',
      transition: theme.transitions.fast,
      border: '1px solid #e0e0e0',
      boxShadow: isHovered ? '0 2px 4px rgba(0,0,0,0.1)' : 'none'
    };
  };

  // Icon style
  const getIconStyle = () => ({
    width: '24px',
    height: '24px',
    transition: theme.transitions.fast
  });





  // Thank you message style
  const thankYouStyle = {
    color: darkMode ? theme.colors.primary.light : theme.colors.primary.main,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    textAlign: 'center',
    padding: '8px 0',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    opacity: fadeOut ? 0 : 1,
    transition: 'opacity 1s ease-out',
    height: fadeOut ? '0' : 'auto',
    overflow: 'hidden',
    marginTop: fadeOut ? '0' : '4px',
    marginBottom: fadeOut ? '0' : '4px'
  };

  // Handle smiley click - submit feedback immediately
  const handleSmileyClick = async (selectedRating) => {
    setRating(selectedRating);

    // Submit feedback immediately
    setIsSubmitting(true);
    setError(null);

    try {
      await onSubmitFeedback(threadId, selectedRating, ''); // No comment, just empty string
      setShowFeedbackForm(false);

      // Reset the fade-out states for the thank you message
      setShowThankYou(true);
      setFadeOut(false);
    } catch (err) {
      setError(err.message || 'Failed to submit feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle smiley hover
  const handleSmileyHover = (hoveredValue) => {
    setHoveredRating(hoveredValue);
  };

  // Handle smiley hover leave
  const handleSmileyLeave = () => {
    setHoveredRating(0);
  };



  // If feedback was already submitted, show thank you message only if not already shown
  if (feedbackSubmitted) {
    // If thank you was already shown or showThankYou is false, don't render anything
    if (thankYouAlreadyShown || !showThankYou) {
      return null;
    }

    return (
      <div style={containerStyle}>
        <div style={thankYouStyle}>
          <img
            src={nbgCheckIcon}
            alt="Check"
            style={{ width: '16px', height: '16px' }}
          />
          <span style={{ fontWeight: 'bold' }}>Ευχαριστούμε για το feedback σας!</span>
        </div>
      </div>
    );
  }

  return (
    <div style={containerStyle}>
      {!showFeedbackForm ? (
        <button
          style={feedbackButtonStyle}
          onClick={() => setShowFeedbackForm(true)}
        >
          <img
            src={satisfactionIcon4}
            alt="Rate us"
            style={{ width: '16px', height: '16px', marginRight: '4px' }}
          />
          Αξιολογήστε με!
        </button>
      ) : (
        <>
          <div style={iconContainerStyle} onMouseLeave={handleSmileyLeave}>
            {[1, 2, 3, 4, 5].map((index) => (
              <div
                key={index}
                onClick={() => handleSmileyClick(index)}
                onMouseEnter={() => handleSmileyHover(index)}
                style={getIconWrapperStyle(index)}
                title={`Rating: ${index}/5`}
              >
                <img
                  src={satisfactionIcons[index - 1]}
                  alt={`Satisfaction ${index}`}
                  style={getIconStyle()}
                />
              </div>
            ))}
          </div>

          {error && (
            <div style={{ color: 'red', fontSize: theme.typography.fontSize.sm, marginTop: '4px' }}>
              {error}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FeedbackWidget;
