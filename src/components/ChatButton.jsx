import theme from '../styles/theme';

// Import NBG chat minimal icon
import nbgChatMinimal from '../../design-assets/nbg-chat-minimal.png';

/**
 * Floating chat button component
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the chat is open
 * @param {boolean} props.hasError - Whether there's an error
 * @param {boolean} props.isLoading - Whether a message is being sent
 * @param {Function} props.onClick - Click handler
 * @param {number} props.unreadCount - Number of unread messages
 * @returns {JSX.Element} - Rendered component
 */
const ChatButton = ({ isOpen, hasError, isLoading, onClick, unreadCount = 0 }) => {
  // Button style
  const chatButtonStyle = {
    width: '60px',
    height: '60px',
    borderRadius: theme.borderRadius.circle,
    background: isOpen
      ? (hasError ? theme.colors.status.error : theme.colors.primary.main)
      : theme.colors.primary.gradient,
    color: theme.colors.text.light,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    boxShadow: theme.shadows.primary,
    fontSize: theme.typography.fontSize.xxl,
    fontWeight: theme.typography.fontWeight.bold,
    transition: theme.transitions.bounce,
    zIndex: theme.zIndex.widget,
    border: 'none',
    transform: isOpen ? 'scale(1) rotate(0deg)' : 'scale(1.05)',
    overflow: 'hidden'
  };

  // Badge style for unread messages
  const badgeStyle = {
    position: 'absolute',
    top: '-6px',
    right: '-6px',
    backgroundColor: theme.colors.status.error,
    color: theme.colors.text.light,
    borderRadius: theme.borderRadius.circle,
    minWidth: '20px',
    height: '20px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '11px',
    fontWeight: theme.typography.fontWeight.bold,
    boxShadow: '0 2px 4px rgba(255, 56, 96, 0.4)',
    border: '2px solid white',
    zIndex: theme.zIndex.above,
    animation: 'pulse 2s infinite',
    padding: '1px 4px'
  };

  return (
    <div onClick={onClick} style={chatButtonStyle}>
         <span style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
          <i className="fas fa-comment" style={{fontSize: '24px'}}></i>
        </span>
    </div>
  );
};

export default ChatButton;
