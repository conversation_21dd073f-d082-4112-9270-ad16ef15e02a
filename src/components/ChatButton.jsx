import theme from '../styles/theme';

// Import NBG chat minimal icon
import nbgChatMinimal from '../../design-assets/nbg-chat-minimal.png';

/**
 * Floating chat button component
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the chat is open
 * @param {boolean} props.hasError - Whether there's an error
 * @param {boolean} props.isLoading - Whether a message is being sent
 * @param {Function} props.onClick - Click handler
 * @param {number} props.unreadCount - Number of unread messages
 * @returns {JSX.Element} - Rendered component
 */
const ChatButton = ({ isOpen, hasError, isLoading, onClick, unreadCount = 0 }) => {
  // Button style
  const chatButtonStyle = {
    width: '60px',
    height: '60px',
    borderRadius: theme.borderRadius.circle,
    background: isOpen
      ? (hasError ? theme.colors.status.error : theme.colors.primary.main)
      : theme.colors.primary.gradient,
    color: theme.colors.text.light,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    boxShadow: theme.shadows.primary,
    fontSize: theme.typography.fontSize.xxl,
    fontWeight: theme.typography.fontWeight.bold,
    transition: theme.transitions.bounce,
    zIndex: theme.zIndex.widget,
    border: 'none',
    transform: isOpen ? 'scale(1) rotate(0deg)' : 'scale(1.05)',
    overflow: 'hidden'
  };

  // Badge style for unread messages
  const badgeStyle = {
    position: 'absolute',
    top: '-6px',
    right: '-6px',
    backgroundColor: theme.colors.status.error,
    color: theme.colors.text.light,
    borderRadius: theme.borderRadius.circle,
    minWidth: '20px',
    height: '20px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '11px',
    fontWeight: theme.typography.fontWeight.bold,
    boxShadow: '0 2px 4px rgba(255, 56, 96, 0.4)',
    border: '2px solid white',
    zIndex: theme.zIndex.above,
    animation: 'pulse 2s infinite',
    padding: '1px 4px'
  };

  return (
    <div onClick={onClick} style={chatButtonStyle}>
      <span style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
        {/* Inline SVG comment icon to replace Font Awesome */}
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="currentColor"
          style={{fontSize: '24px'}}
        >
          <path d="M12 2C6.48 2 2 6.48 2 12c0 1.54.36 3.04.97 4.37L1 23l6.63-1.97C9.96 21.64 11.46 22 13 22h7c1.1 0 2-.9 2-2V12c0-5.52-4.48-10-10-10zm8 18h-7c-1.54 0-3.04-.36-4.37-.97L4 20l1.03-4.63C4.36 14.04 4 12.54 4 11c0-4.41 3.59-8 8-8s8 3.59 8 8v9z"/>
        </svg>
      </span>
    </div>
  );
};

export default ChatButton;
