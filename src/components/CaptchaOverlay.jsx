import { useEffect } from 'react';
import theme from '../styles/theme';

/**
 * Captcha overlay component
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the chat is open
 * @param {boolean} props.captchaPassed - Whether captcha verification passed
 * @param {string} props.captchaError - Captcha error message
 * @param {Function} props.setCaptchaPassed - Function to set captcha passed state
 * @param {Function} props.setCaptchaError - Function to set captcha error state
 * @returns {JSX.Element|null} - Rendered component or null if not needed
 */
const CaptchaOverlay = ({
  isOpen,
  captchaPassed,
  captchaError,
  setCaptchaPassed,
  setCaptchaError
}) => {
  // Load and mount Google reCAPTCHA widget dynamically when overlay is shown
  useEffect(() => {
    if (!isOpen || captchaPassed) return;

    let intervalId;

    // Function to load reCAPTCHA script dynamically
    function loadRecaptchaScript() {
      return new Promise((resolve, reject) => {
        // Check if already loaded
        if (window.grecaptcha) {
          resolve();
          return;
        }

        // Check if script is already being loaded
        if (document.querySelector('script[src*="recaptcha"]')) {
          // Wait for it to load
          const checkLoaded = () => {
            if (window.grecaptcha) {
              resolve();
            } else {
              setTimeout(checkLoaded, 100);
            }
          };
          checkLoaded();
          return;
        }

        // Load the script
        const script = document.createElement('script');
        script.src = 'https://www.google.com/recaptcha/api.js';
        script.async = true;
        script.defer = true;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load reCAPTCHA'));
        document.head.appendChild(script);
      });
    }

    function tryRenderRecaptcha() {
      if (
        window.grecaptcha &&
        typeof window.grecaptcha.render === 'function' &&
        document.getElementById('recaptcha-widget') &&
        !document.getElementById('recaptcha-widget').hasChildNodes()
      ) {
        window.grecaptcha.render('recaptcha-widget', {
          sitekey: '6LfDVEMrAAAAAECe1o_VW-hjz0ipeEp2h-BbhsGV',
          callback: async (token) => {
            setCaptchaPassed(true);
            // Store the reCAPTCHA token in localStorage for later API use
            try {
              localStorage.setItem('recaptchaToken', token);
            } catch (e) {
              // Fallback: ignore storage errors
            }
          },
          'expired-callback': () => {
            setCaptchaError('Captcha expired. Please try again.');
          }
        });
        clearInterval(intervalId);
      }
    }

    // Load reCAPTCHA script and then try to render
    loadRecaptchaScript()
      .then(() => {
        intervalId = setInterval(tryRenderRecaptcha, 200);
      })
      .catch((error) => {
        setCaptchaError('Failed to load captcha. Please refresh the page.');
        console.error('reCAPTCHA loading error:', error);
      });

    // Clean up: remove widget if component unmounts or closes
    return () => {
      if (intervalId) clearInterval(intervalId);
      const el = document.getElementById('recaptcha-widget');
      if (el) el.innerHTML = '';
    };
  }, [isOpen, captchaPassed, setCaptchaPassed, setCaptchaError]);

  // Don't render if captcha is passed or chat is closed
  if (captchaPassed || !isOpen) return null;

  return (
    <div
      style={{
        position: 'absolute',
        zIndex: theme.zIndex.modal,
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'rgba(255,255,255,0.97)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <div style={{
        marginBottom: 24,
        fontWeight: theme.typography.fontWeight.semiBold,
        fontSize: theme.typography.fontSize.xl,
        color: theme.colors.primary.main
      }}>
        Please complete the captcha to access the chat
      </div>
      <div id="recaptcha-widget"></div>
      {captchaError && (
        <div style={{ color: theme.colors.status.error, marginTop: 12 }}>
          {captchaError}
        </div>
      )}
    </div>
  );
};

export default CaptchaOverlay;
