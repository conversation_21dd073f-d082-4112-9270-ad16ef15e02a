/**
 * Minimal Chat Bubble Widget Loader
 * 
 * This is a minimal loader script that only requires the UMD bundle.
 * No external dependencies needed - everything is self-contained.
 */

(function() {
  'use strict';

  // Function to check if the widget is loaded
  function isWidgetLoaded() {
    return window.ChatBubbleWidget && typeof window.ChatBubbleWidget.mount === 'function';
  }

  // Function to wait for the widget to load
  function waitForWidget(callback, maxAttempts = 50, interval = 100) {
    let attempts = 0;

    const checkWidget = function() {
      if (isWidgetLoaded()) {
        callback();
      } else if (attempts < maxAttempts) {
        attempts++;
        setTimeout(checkWidget, interval);
      } else {
        console.error('ChatBubbleWidget failed to load after multiple attempts');
      }
    };

    checkWidget();
  }

  // Expose the loader globally
  window.ChatBubbleLoader = {
    mount: function(targetId, config = {}) {
      waitForWidget(function() {
        window.ChatBubbleWidget.mount(targetId, config);
      });
    },
    
    isLoaded: isWidgetLoaded
  };

  // Auto-mount if there's a container with id 'chat-bubble-root'
  document.addEventListener('DOMContentLoaded', function() {
    const autoMountContainer = document.getElementById('chat-bubble-root');
    if (autoMountContainer) {
      // Check for configuration in data attributes
      const config = {
        apiUrl: autoMountContainer.dataset.apiUrl || '/api/cc-lg'
      };
      
      window.ChatBubbleLoader.mount('chat-bubble-root', config);
    }
  });

})();
