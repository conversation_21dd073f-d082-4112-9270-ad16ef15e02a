/**
 * Chat Bubble Widget Loader
 *
 * This script loads the Chat Bubble Widget and mounts it to a specified DOM element.
 * It can be included in any HTML page to add the chat widget functionality.
 *
 * Usage:
 * 1. Include this script in your HTML
 * 2. Call window.ChatBubbleWidget.mount('your-container-id', { apiUrl: '/your-api-endpoint' })
 */

(function() {
  // Check if React and ReactDOM are already loaded
  const hasReact = typeof window.React !== 'undefined';
  const hasReactDOM = typeof window.ReactDOM !== 'undefined';

  // Store original console.error to restore it later
  const originalConsoleError = console.error;

  // Function to load script asynchronously
  function loadScript(src, callback) {
    const script = document.createElement('script');
    script.src = src;
    script.async = true;
    script.onload = callback;
    script.onerror = function() {
      console.error('Failed to load script:', src);
    };
    document.head.appendChild(script);
  }

  // Function to load CSS asynchronously
  function loadCSS(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    document.head.appendChild(link);
  }

  // Function to initialize the widget once dependencies are loaded
  function initializeWidget() {
    // Restore original console.error
    console.error = originalConsoleError;

    // Define the mount function that will be exposed globally
    window.ChatBubbleWidget = {
      mount: function(targetId, config = {}) {
        const target = document.getElementById(targetId);
        if (!target) {
          console.error('ChatBubbleWidget: Target element not found:', targetId);
          return;
        }

        try {
          // Import the ChatWidget component
          import('./chat-bubble-component.jsx').then(module => {
            const ChatWidget = module.default;
            // Create a React root and render the component
            window.ReactDOM.createRoot(target).render(
              window.React.createElement(ChatWidget, {
                apiUrl: config.apiUrl || undefined
              })
            );
            console.log('ChatBubbleWidget: Successfully mounted to', targetId);
          }).catch(err => {
            console.error('ChatBubbleWidget: Failed to load component:', err);
          });
        } catch (error) {
          console.error('ChatBubbleWidget: Error mounting widget:', error);
        }
      }
    };
  }

  // Suppress React development mode warnings during initialization
  console.error = function(msg) {
    if (msg && typeof msg === 'string' &&
        (msg.includes('React.createElement') ||
         msg.includes('Warning: '))) {
      return;
    }
    originalConsoleError.apply(console, arguments);
  };

  // Load dependencies if needed
  if (!hasReact) {
    loadScript('https://unpkg.com/react@18/umd/react.production.min.js', function() {
      if (!hasReactDOM) {
        loadScript('https://unpkg.com/react-dom@18/umd/react-dom.production.min.js', initializeWidget);
      } else {
        initializeWidget();
      }
    });
  } else if (!hasReactDOM) {
    loadScript('https://unpkg.com/react-dom@18/umd/react-dom.production.min.js', initializeWidget);
  } else {
    // Both React and ReactDOM are already loaded
    initializeWidget();
  }

  // Load Font Awesome if not already loaded
  if (!document.querySelector('link[href*="font-awesome"]')) {
    loadCSS('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css');
  }
})();
