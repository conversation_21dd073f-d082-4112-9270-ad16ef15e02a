/**
 * Utility functions for message handling
 */

// Mapping of text emoticons to emoji characters
export const emoticonMap = {
  ':)': '😊',
  ':-)': '😊',
  ':D': '😃',
  ':-D': '😃',
  ':(': '😞',
  ':-(': '😞',
  ';)': '😉',
  ';-)': '😉',
  ':p': '😛',
  ':P': '😛',
  ':-p': '😛',
  ':-P': '😛',
  ':o': '😮',
  ':O': '😮',
  '<3': '❤️',
  'XD': '😂',
  'xD': '😂',
  ':/': '😕',
  ':-/': '😕',
  ':"(': '😢',
  '8)': '😎',
  '8-)': '😎',
  '^_^': '😄'
};

/**
 * Convert text emoticons to emoji characters
 * @param {string} text - Text to convert
 * @returns {string} - Text with emoticons converted to emojis
 */
export const convertEmoticonsToEmojis = (text) => {
  let convertedText = text;

  // Create a regex pattern from the emoticon keys, escaping special characters
  const pattern = Object.keys(emoticonMap)
    .map(key => key.replace(/([.*+?^=!:${}()|[\]/\\])/g, '\\$1'))
    .join('|');

  // Replace emoticons with emojis
  if (pattern) {
    const regex = new RegExp(pattern, 'g');
    convertedText = text.replace(regex, match => emoticonMap[match] || match);
  }

  return convertedText;
};

/**
 * Format timestamp to a user-friendly format
 * @param {string} timestamp - ISO timestamp
 * @returns {string} - Formatted timestamp
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const now = new Date();
  const isToday = date.toDateString() === now.toDateString();
  const isYesterday = new Date(now - 86400000).toDateString() === date.toDateString();

  // Format: HH:MM for today, "Yesterday" for yesterday, MM/DD HH:MM for other days
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  if (isToday) {
    return `${hours}:${minutes}`;
  } else if (isYesterday) {
    return `Yesterday ${hours}:${minutes}`;
  } else {
    // Check if it's the current year
    const isCurrentYear = date.getFullYear() === now.getFullYear();
    if (isCurrentYear) {
      return `${month}/${day} ${hours}:${minutes}`;
    } else {
      // Include the year for dates from previous years
      return `${month}/${day}/${date.getFullYear().toString().substr(2)} ${hours}:${minutes}`;
    }
  }
};

/**
 * Get full timestamp format for tooltip/title
 * @param {string} timestamp - ISO timestamp
 * @returns {string} - Full formatted timestamp
 */
export const getFullTimestamp = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const options = {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  };

  return date.toLocaleString(undefined, options);
};
