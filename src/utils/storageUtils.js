/**
 * Utility functions for localStorage operations
 */

/**
 * Storage keys used in the application
 */
export const STORAGE_KEYS = {
  IS_OPEN: 'chatWidgetOpen',
  MESSAGES: 'chatWidgetMessages',
  THREAD_ID: 'chatWidgetThreadId',
  DARK_MODE: 'chatWidgetDarkMode',
  WINDOW_WIDTH: 'chatWidgetWidth',
  FEEDBACK: 'chatWidgetFeedback'
};

/**
 * Get item from localStorage with fallback value
 * @param {string} key - Storage key
 * @param {*} defaultValue - Default value if key doesn't exist
 * @returns {*} - Parsed value from localStorage or default value
 */
export const getStorageItem = (key, defaultValue) => {
  try {
    const item = localStorage.getItem(key);

    // If item doesn't exist, return default value
    if (!item) return defaultValue;

    // Handle special case for threadId which might be a UUID string
    if (key === STORAGE_KEYS.THREAD_ID) {
      // If it looks like a UUID (has dashes and is the right length), return as is
      if (typeof item === 'string' &&
          (item.includes('-') || item.length === 36 || item.length === 32)) {
        // Remove any quotes that might have been added
        return item.replace(/^"|"$/g, '');
      }
    }

    // Try to parse as JSON
    try {
      return JSON.parse(item);
    } catch (parseError) {
      // If parsing fails, return the raw value for strings
      if (typeof item === 'string') {
        return item;
      }
      throw parseError; // Re-throw for other types
    }
  } catch (error) {
    console.error(`Error getting item from localStorage: ${key}`, error);
    // Clear the problematic item to prevent future errors
    try {
      localStorage.removeItem(key);
    } catch (e) {
      // Ignore errors when removing
    }
    return defaultValue;
  }
};

/**
 * Set item in localStorage
 * @param {string} key - Storage key
 * @param {*} value - Value to store
 */
export const setStorageItem = (key, value) => {
  try {
    // Special handling for thread ID to ensure it's stored as a plain string
    if (key === STORAGE_KEYS.THREAD_ID && typeof value === 'string') {
      localStorage.setItem(key, value);
    } else {
      localStorage.setItem(key, JSON.stringify(value));
    }
  } catch (error) {
    console.error(`Error setting item in localStorage: ${key}`, error);
  }
};

/**
 * Remove item from localStorage
 * @param {string} key - Storage key
 */
export const removeStorageItem = (key) => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing item from localStorage: ${key}`, error);
  }
};

