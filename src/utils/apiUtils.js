/**
 * Utility functions for API communication
 */

/**
 * Send message to thread with error handling
 * @param {string} apiUrl - API base URL
 * @param {string} threadId - Thread ID
 * @param {string} message - User message
 * @returns {Promise<Object>} - Response payload
 */
export const sendMessageToThread = async (apiUrl, threadId, message) => {
  console.log('Sending message:', { threadId, message });
  try {
    const response = await fetch(`${apiUrl}/threads/${threadId}/runs/wait`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-ReCaptcha-Token': localStorage.getItem('recaptchaToken') || ''
      },
      body: JSON.stringify({
        header: {
          ID: crypto.randomUUID(),
          Application: "A7459F80-5761-4E53-9270-B1FFA5C91D4F",
          Bank: "NBG",
          AgentId: ""
        },
        payload: {
          UserMessage: message
        }
      })
    });

    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.exception?.message || 'Failed to send message');
    }

    // Validate agent response
    if (!data.payload || !data.payload.agentMessage) {
      throw new Error('No response received from agent');
    }

    return data.payload;
  } catch (error) {
    console.error('Error sending message:', error);
    throw new Error(error.message || 'Failed to send message');
  }
};

/**
 * Generate a new thread ID
 * @returns {string} - New thread ID
 */
export const generateThreadId = () => {
  return crypto.randomUUID();
};

/**
 * Send feedback for a thread
 * @param {string} apiUrl - API base URL
 * @param {string} threadId - Thread ID the feedback is for
 * @param {number} rating - Star rating (1-5)
 * @param {string} comment - Optional feedback comment
 * @returns {Promise<Object>} - Response payload
 */
export const sendFeedback = async (apiUrl, threadId, rating, comment = '') => {
  console.log('Sending feedback:', { threadId, rating, comment });
  try {
    const response = await fetch(`${apiUrl}/threads/${threadId}/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-ReCaptcha-Token': localStorage.getItem('recaptchaToken') || ''
      },
      body: JSON.stringify({
        header: {
          ID: crypto.randomUUID(),
          Application: "A7459F80-5761-4E53-9270-B1FFA5C91D4F",
          Bank: "NBG",
          AgentId: ""
        },
        payload: {
          rating,
          comment
        }
      })
    });

    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.exception?.message || 'Failed to send feedback');
    }

    return data.payload;
  } catch (error) {
    console.error('Error sending feedback:', error);
    throw new Error(error.message || 'Failed to send feedback');
  }
};
