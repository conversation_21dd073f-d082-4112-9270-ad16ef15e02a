// UMD entry for Chat Bubble Widget
import React from "react";
import <PERSON>actD<PERSON> from "react-dom/client";
import ChatWidget from "./components/ChatWidget.jsx";

// Export the loader script
import "./chat-bubble-loader.js";

// Mount function for direct usage
function mount(targetId, config = {}) {
  const target = document.getElementById(targetId);
  if (!target) {
    console.error("ChatBubbleWidget: target div not found:", targetId);
    return;
  }
  // Use React to create the element
  const element = React.createElement(ChatWidget, { apiUrl: config.apiUrl });
  ReactDOM.createRoot(target).render(element);
}

// Expose the mount function globally
window.ChatBubbleWidget = { mount };