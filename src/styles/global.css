/* Global styles for the chat widget */

/* Animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes blink {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

/* Icon animations - Updated to use generic selectors instead of Font Awesome classes */
.chat-icon,
.send-icon,
.close-icon {
  animation: fadeIn 0.3s ease-in-out;
}

.send-icon:hover,
.close-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Typing animation */
.typing-animation .dot {
  animation: blink 1.4s infinite;
  display: inline-block;
}

.typing-animation .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-animation .dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Input focus styles */
.chat-input:focus {
  border-color: #0b2d7a;
  box-shadow: 0 0 0 2px rgba(11, 45, 122, 0.2);
}

.chat-input::placeholder {
  opacity: 0.8;
}

/* Resize handle styles */
.resize-handle:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.resize-handle:hover > div > div {
  background-color: rgba(0, 0, 0, 0.4);
}

.resize-handle:active {
  background-color: rgba(0, 0, 0, 0.12);
}

.resize-handle:active > div > div {
  background-color: rgba(0, 0, 0, 0.6);
}

/* Dark mode overrides */
.dark-mode .resize-handle:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.dark-mode .resize-handle:hover > div > div {
  background-color: rgba(255, 255, 255, 0.6);
}

.dark-mode .resize-handle:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .resize-handle:active > div > div {
  background-color: rgba(255, 255, 255, 0.8);
}

.dark-mode .chat-input:focus {
  background-color: #4a4a5c;
}

.dark-mode .chat-input::placeholder {
  color: #aaa;
}

/* Timestamp hover effect */
.timestamp-display:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9);
}

.dark-mode .timestamp-display:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
}

/* Resizing cursor */
body.resizing {
  cursor: ew-resize !important;
  user-select: none;
}

/* Markdown styles */
a {
  text-decoration: underline;
}

code {
  background-color: rgba(0,0,0,0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

.dark-mode code {
  background-color: rgba(0,0,0,0.3);
}

/* Ensure the chat widget container doesn't inherit styles from the parent page */
.chat-widget-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.chat-widget-container a,
.chat-widget-container button,
.chat-widget-container input {
  outline: none;
}

.chat-widget-container a:focus,
.chat-widget-container button:focus,
.chat-widget-container input:focus {
  outline: none;
}
