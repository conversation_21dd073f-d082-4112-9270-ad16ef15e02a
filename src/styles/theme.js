/**
 * Theme configuration for the chat widget
 * Contains colors, spacing, and other design tokens
 */

const theme = {
  colors: {
    // NBG Primary colors
    primary: {
      main: '#003841',        // NBG Dark Teal
      light: '#007B85',       // NBG Light Teal
      dark: '#00626A',        // NBG Medium Teal
      gradient: 'linear-gradient(135deg, #003841 0%, #007B85 100%)',
      contrastText: '#ffffff',
    },
    // NBG Secondary colors
    secondary: {
      main: '#F5F8F6',        // NBG Main background
      light: '#ffffff',       // White
      dark: '#D5EFF4',        // Profile icon background
    },
    // Status colors
    status: {
      success: '#28a745',
      warning: '#ffc107',
      error: '#dc3545',
      info: '#17a2b8',
    },
    // NBG Text colors
    text: {
      primary: '#212121',     // NBG Primary text
      secondary: '#6A6C6A',   // NBG Secondary text
      disabled: '#949794',    // NBG Muted text
      light: '#ffffff',
    },
    // Dark mode colors
    dark: {
      background: '#1e1e2d',
      surface: '#2a2a3c',
      input: '#3a3a4c',
      border: 'rgba(255, 255, 255, 0.05)',
      text: '#e1e1e6',
    },
    // NBG Light mode colors
    light: {
      background: '#ffffff',
      surface: '#F5F8F6',      // NBG Main background
      input: '#F8F9FA',        // NBG Input background
      border: '#E1E5E9',       // NBG Light borders
      text: '#212121',         // NBG Primary text
    },
    // NBG specific colors
    nbg: {
      borderAccent: '#007B85',  // NBG Accent borders
      profileBg: '#D5EFF4',     // Profile icon background
      inputBorder: '#E1E5E9',   // Input field borders
      suggestionBorder: '#007B85', // Suggestion button borders
    },
  },

  // Spacing scale
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },

  // Border radius
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',              // NBG container radius
    circle: '50%',
    messageBubble: {
      user: '8px 8px 8px 0px',   // NBG user bubble (right-aligned)
      agent: '8px 8px 8px 0px',  // NBG bot bubble top
      agentMiddle: '2px 8px 8px 2px', // NBG bot bubble middle
      agentBottom: '8px 8px 0px 8px', // NBG bot bubble bottom
    },
    suggestion: '8px',           // NBG suggestion buttons
    input: '24px',              // NBG input field
  },

  // Typography
  typography: {
    fontFamily: '"Aeonik Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
    fontSize: {
      xs: '10px',
      sm: '12px',
      md: '14px',
      lg: '16px',
      xl: '18px',
      xxl: '24px',
    },
    fontWeight: {
      regular: 400,
      medium: 500,
      semiBold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      loose: 1.8,
    },
  },

  // Shadows
  shadows: {
    small: '0 2px 8px rgba(0, 0, 0, 0.1)',
    medium: '0 4px 12px rgba(0, 0, 0, 0.15)',
    large: '0 8px 24px rgba(0, 0, 0, 0.2)',
    primary: '0 4px 15px rgba(11, 45, 122, 0.4)',
    nbg: '0px 6px 15px 5px rgba(0, 56, 65, 0.1)', // NBG main shadow
    nbgHover: '0px 8px 20px 8px rgba(0, 56, 65, 0.15)', // NBG hover shadow
  },

  // Transitions
  transitions: {
    fast: 'all 0.2s ease',
    normal: 'all 0.3s ease',
    slow: 'all 0.5s ease',
    bounce: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },

  // Z-index scale
  zIndex: {
    base: 1,
    above: 10,
    dropdown: 100,
    sticky: 200,
    fixed: 300,
    modal: 400,
    popover: 500,
    tooltip: 600,
    toast: 700,
    widget: 9999,
  },
};

export default theme;
