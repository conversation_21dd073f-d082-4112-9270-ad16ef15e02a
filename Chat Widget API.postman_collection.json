{"info": {"_postman_id": "0e528e8d-73d9-4b27-8604-fc9eff18b014", "name": "Chat Widget API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "1302163", "_collection_link": "https://blue-astronaut-140402.postman.co/workspace/NBG~a5c2cdb8-ea16-47d0-bc70-7e68ea35e56a/collection/1302163-0e528e8d-73d9-4b27-8604-fc9eff18b014?action=share&source=collection_link&creator=1302163"}, "item": [{"name": "NBG Proxy QA Copy", "item": [{"name": "Create Thread", "event": [{"listen": "test", "script": {"exec": ["var responseData = pm.response.json();\r", "pm.environment.set(\"thread_id\", responseData.payload.threadId);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:44326/cc-lg/threads", "protocol": "https", "host": ["localhost"], "port": "44326", "path": ["cc-lg", "threads"]}}, "response": [{"name": "Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg/threads", "protocol": "https", "host": ["servicesqa", "nbg", "gr"], "path": ["apis", "Nbg.NetCore.AI.Agents.Proxy", "cc-lg", "threads"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Cache-Control", "value": "no-cache"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Expires", "value": "-1"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinity=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinitySameSite=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;SameSite=None;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Strict-Transport-Security", "value": "max-age=480;includeSubdomains;preload"}, {"key": "Date", "value": "Mon, 19 May 2025 06:40:23 GMT"}], "cookie": [], "body": "{\n    \"payload\": {\n        \"threadId\": \"eec0b883-9a57-40a6-b04a-aae3c740b98e\"\n    },\n    \"exception\": null,\n    \"messages\": null,\n    \"executionTime\": 267.7423\n}"}]}, {"name": "Send Message in existing thread", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"header\": {\r\n        \"ID\": \"035515fe-5d76-44c2-97ef-145cd4ac88b6\",\r\n        \"Application\": \"A7459F80-5761-4E53-9270-B1FFA5C91D4F\",\r\n        \"Bank\": \"NBG\",\r\n        \"AgentId\": \"\"\r\n    },\r\n    \"payload\": {\r\n        \"UserMessage\": \"Τι αφορούν ακριβώς αυτές οι πληροφορίες;\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg/threads/{{thread_id}}/runs/wait", "protocol": "https", "host": ["servicesqa", "nbg", "gr"], "path": ["apis", "Nbg.NetCore.AI.Agents.Proxy", "cc-lg", "threads", "{{thread_id}}", "runs", "wait"]}}, "response": [{"name": "Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"header\": {\r\n        \"ID\": \"035515fe-5d76-44c2-97ef-145cd4ac88b6\",\r\n        \"Application\": \"A7459F80-5761-4E53-9270-B1FFA5C91D4F\",\r\n        \"Bank\": \"NBG\",\r\n        \"AgentId\": \"\"\r\n    },\r\n    \"payload\": {\r\n        \"UserMessage\": \"Καλησπέρα!\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg/threads/{{thread_id}}/runs/wait", "protocol": "https", "host": ["servicesqa", "nbg", "gr"], "path": ["apis", "Nbg.NetCore.AI.Agents.Proxy", "cc-lg", "threads", "{{thread_id}}", "runs", "wait"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Cache-Control", "value": "no-cache"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Expires", "value": "-1"}, {"key": "Location", "value": "/threads/eec0b883-9a57-40a6-b04a-aae3c740b98e/runs/1f0347c3-3150-61d0-9a1b-d7c7124d3254/join"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinity=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinitySameSite=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;SameSite=None;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Strict-Transport-Security", "value": "max-age=480;includeSubdomains;preload"}, {"key": "Date", "value": "Mon, 19 May 2025 06:40:46 GMT"}], "cookie": [], "body": "{\n    \"payload\": {\n        \"agentMessage\": \"Καλησπέρα σας! 😊 Πώς μπορώ να σας εξυπηρετήσω σήμερα;\",\n        \"threadId\": \"eec0b883-9a57-40a6-b04a-aae3c740b98e\"\n    },\n    \"exception\": null,\n    \"messages\": null,\n    \"executionTime\": 8014.8119\n}"}]}, {"name": "Send Message in new thread", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"header\": {\r\n        \"ID\": \"035515fe-5d76-44c2-97ef-145cd4ac88b6\",\r\n        \"Application\": \"A7459F80-5761-4E53-9270-B1FFA5C91D4F\",\r\n        \"Bank\": \"NBG\",\r\n        \"AgentId\": \"\"\r\n    },\r\n    \"payload\": {\r\n        \"UserMessage\": \"Τι αφορούν ακριβώς αυτές οι πληροφορίες;\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg/threads/{{$guid}}/runs/wait", "protocol": "https", "host": ["servicesqa", "nbg", "gr"], "path": ["apis", "Nbg.NetCore.AI.Agents.Proxy", "cc-lg", "threads", "{{$guid}}", "runs", "wait"]}}, "response": [{"name": "Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"header\": {\r\n        \"ID\": \"035515fe-5d76-44c2-97ef-145cd4ac88b6\",\r\n        \"Application\": \"A7459F80-5761-4E53-9270-B1FFA5C91D4F\",\r\n        \"Bank\": \"NBG\",\r\n        \"AgentId\": \"\"\r\n    },\r\n    \"payload\": {\r\n        \"UserMessage\": \"Τι αφορούν ακριβώς αυτές οι πληροφορίες;\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg/threads/{{$guid}}/runs/wait", "protocol": "https", "host": ["servicesqa", "nbg", "gr"], "path": ["apis", "Nbg.NetCore.AI.Agents.Proxy", "cc-lg", "threads", "{{$guid}}", "runs", "wait"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Cache-Control", "value": "no-cache"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Expires", "value": "-1"}, {"key": "Location", "value": "/threads/7e8c7ce0-93c7-444a-8231-5589755faec9/runs/1f0347cc-a689-60bb-99b9-776c7173ec81/join"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinity=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinitySameSite=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;SameSite=None;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Strict-Transport-Security", "value": "max-age=480;includeSubdomains;preload"}, {"key": "Date", "value": "Mon, 19 May 2025 06:45:18 GMT"}], "cookie": [], "body": "{\n    \"payload\": {\n        \"agentMessage\": \"Οι πληροφορίες που παρέχονται αφορούν διάφορες τραπεζικές υπηρεσίες, διαδικασίες και προϊόντα. Παρακάτω είναι μια συνοπτική περιγραφή των θεμάτων που καλύπτονται:\\n\\n### 1. **Αφορολόγητο**\\n   - Πληρωμές που συμμετέχουν στο αφορολόγητο.\\n   - Συναλλαγές μέσω καρτών, Digital Banking, και εφαρμογών όπως το Next by NBG.\\n   - Εξαιρέσεις για ατομικές επιχειρήσεις.\\n\\n### 2. **Συναλλαγές και Ασφάλεια**\\n   - Τι είναι το Phishing και το Spear Phishing.\\n   - Πληροφορίες για την ασφάλεια προσωπικών δεδομένων.\\n   - Διαδικασίες για την αποφυγή απάτης.\\n\\n### 3. **Κάρτες και Συναλλαγές**\\n   - Πληροφορίες για τη χρήση καρτών σε διαδικτυακές αγορές.\\n   - Ειδοποιήσεις (Alerts) για συναλλαγές.\\n   - Συμμετοχή συναλλαγών μέσω wallets (Apple Pay, Google Pay) στο αφορολόγητο.\\n\\n### 4. **Δικαιολογητικά και Διαδικασίες**\\n   - Έγγραφα που απαιτούνται για διάφορες τραπεζικές υπηρεσίες (π.χ., ταυτότητα, Ε9, εκκαθαριστικό).\\n   - Διαδικασίες για την απόκτηση προϊόντων όπως προπληρωμένες κάρτες ή προσωπικά δάνεια.\\n\\n### 5. **Λογαριασμοί και Βεβαιώσεις**\\n   - Πληροφορίες για λογαριασμούς κοινής ωφέλειας.\\n   - Αντίγραφα κίνησης καταθετικών λογαριασμών.\\n   - Αμφισβήτηση συναλλαγών.\\n\\n### 6. **Προϊόντα και Υπηρεσίες**\\n   - Προσωπικά δάνεια και τα πλεονεκτήματά τους.\\n   - Υπηρεσίες Private Banking.\\n   - Δυνατότητα λήψης εμπλουτισμένων αρχείων Excel μέσω Internet Banking.\\n\\n### 7. **Φορολογικά και Νομικά Θέματα**\\n   - Απαλλαγή τραπεζικών εργασιών από ΦΠΑ.\\n   - Ιατρικές δαπάνες που συμμετέχουν στο αφορολόγητο.\\n\\nΑν χρειάζεστε περισσότερες πληροφορίες για κάποιο συγκεκριμένο θέμα, παρακαλώ διευκρινίστε!\",\n        \"threadId\": \"7e8c7ce0-93c7-444a-8231-5589755faec9\"\n    },\n    \"exception\": null,\n    \"messages\": null,\n    \"executionTime\": 27093.4222\n}"}]}, {"name": "Get Conversation History", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"header\": {\r\n        \"ID\": \"035515fe-5d76-44c2-97ef-145cd4ac88b6\",\r\n        \"Application\": \"A7459F80-5761-4E53-9270-B1FFA5C91D4F\",\r\n        \"Bank\": \"NBG\",\r\n        \"AgentId\": \"\"\r\n    },\r\n    \"payload\": {\r\n        \"UserMessage\": \"Τι αφορούν ακριβώς αυτές οι πληροφορίες;\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg/threads/{{thread_id}}/history", "protocol": "https", "host": ["servicesqa", "nbg", "gr"], "path": ["apis", "Nbg.NetCore.AI.Agents.Proxy", "cc-lg", "threads", "{{thread_id}}", "history"]}}, "response": [{"name": "Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"header\": {\r\n        \"ID\": \"035515fe-5d76-44c2-97ef-145cd4ac88b6\",\r\n        \"Application\": \"A7459F80-5761-4E53-9270-B1FFA5C91D4F\",\r\n        \"Bank\": \"NBG\",\r\n        \"AgentId\": \"\"\r\n    },\r\n    \"payload\": {\r\n        \"UserMessage\": \"Τι αφορούν ακριβώς αυτές οι πληροφορίες;\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://servicesqa.nbg.gr/apis/Nbg.NetCore.AI.Agents.Proxy/cc-lg/threads/{{thread_id}}/history", "protocol": "https", "host": ["servicesqa", "nbg", "gr"], "path": ["apis", "Nbg.NetCore.AI.Agents.Proxy", "cc-lg", "threads", "{{thread_id}}", "history"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Cache-Control", "value": "no-cache"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Expires", "value": "-1"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinity=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Set-<PERSON><PERSON>", "value": "ARRAffinitySameSite=12ad29c2d83712da2c90b5274d009bf95b7bd6acdbd60d3c5b256e877c2970a4;Path=/;HttpOnly;SameSite=None;Secure;Domain=nbg-webapp-langgraph-cc-we-qa-01.azurewebsites.net"}, {"key": "Strict-Transport-Security", "value": "max-age=480;includeSubdomains;preload"}, {"key": "Date", "value": "Mon, 19 May 2025 06:45:39 GMT"}], "cookie": [], "body": "{\n    \"payload\": {\n        \"messages\": [\n            {\n                \"content\": \"Καλησπέρα!\",\n                \"type\": \"userMessage\"\n            },\n            {\n                \"content\": \"Καλησπέρα σας! 😊 Πώς μπορώ να σας εξυπηρετήσω σήμερα;\",\n                \"type\": \"agentMessage\"\n            }\n        ]\n    },\n    \"exception\": null,\n    \"messages\": null,\n    \"executionTime\": 253.7013\n}"}]}]}]}