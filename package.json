{"name": "widget", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && cp src/minimal-loader.js dist/chat-bubble-loader.js", "build:dev": "vite build --mode development && cp src/minimal-loader.js dist/chat-bubble-loader.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@langchain/core": "^0.3.51", "@langchain/langgraph-sdk": "^0.0.74", "axios": "^1.9.0", "dotenv": "^16.5.0", "emoji-picker-react": "^4.12.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1", "vite-plugin-environment": "^1.1.3"}}