# Chat Widget Mock Server

This is a mock server for the Chat Widget application. It simulates the backend API and provides random responses to chat messages.

## Features

- Creates and manages chat threads
- Generates random Greek responses to user messages
- Provides contextual responses based on keywords in user messages
- Handles feedback submission for chat threads
- Simulates network latency with random response times

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Start the server:
   ```
   npm start
   ```

   Or for development with auto-restart:
   ```
   npm run dev
   ```

## API Endpoints

### Threads

- `POST /cc-lg/threads` - Create a new thread
- `GET /cc-lg/threads/:threadId` - Get thread details
- `POST /cc-lg/threads/:threadId/runs/wait` - Send a message to a thread and wait for response

### Feedback

- `POST /cc-lg/threads/:threadId/feedback` - Submit feedback for a thread
- `GET /cc-lg/threads/feedback` - Get all feedback

## Request/Response Examples

### Create a new thread

**Request:**
```
POST /cc-lg/threads
```

**Response:**
```json
{
  "payload": {
    "threadId": "eec0b883-9a57-40a6-b04a-aae3c740b98e"
  },
  "exception": null,
  "messages": null,
  "executionTime": 1234
}
```

### Send a message to a thread

**Request:**
```
POST /cc-lg/threads/:threadId/runs/wait
```

**Body:**
```json
{
  "header": {
    "ID": "035515fe-5d76-44c2-97ef-145cd4ac88b6",
    "Application": "A7459F80-5761-4E53-9270-B1FFA5C91D4F",
    "Bank": "NBG",
    "AgentId": ""
  },
  "payload": {
    "UserMessage": "Καλησπέρα!"
  }
}
```

**Response:**
```json
{
  "payload": {
    "agentMessage": "Καλησπέρα! Πώς μπορώ να σας βοηθήσω σήμερα;",
    "threadId": "eec0b883-9a57-40a6-b04a-aae3c740b98e"
  },
  "exception": null,
  "messages": null,
  "executionTime": 2345
}
```

### Submit feedback for a thread

**Request:**
```
POST /cc-lg/threads/:threadId/feedback
```

**Body:**
```json
{
  "header": {
    "ID": "035515fe-5d76-44c2-97ef-145cd4ac88b6",
    "Application": "A7459F80-5761-4E53-9270-B1FFA5C91D4F",
    "Bank": "NBG",
    "AgentId": ""
  },
  "payload": {
    "rating": 5,
    "comment": "Πολύ καλή εξυπηρέτηση!"
  }
}
```

**Response:**
```json
{
  "payload": {
    "success": true,
    "message": "Feedback submitted successfully"
  },
  "exception": null,
  "messages": null,
  "executionTime": 1234
}
```

## Integration with the Chat Widget

To use this mock server with the Chat Widget, set the API URL to:

```
http://localhost:3000/cc-lg
```

This can be done by:

1. Setting the `VITE_API_URL` environment variable
2. Passing the `apiUrl` prop to the ChatWidget component
3. Using the `apiUrl` parameter when mounting the widget
