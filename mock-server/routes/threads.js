const express = require('express');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

// In-memory storage for threads and feedback
const threads = {};
const feedback = {};

// Helper function to generate random response time (1-3 seconds)
const getRandomResponseTime = () => Math.floor(Math.random() * 2000) + 1000;

// Helper function to get Greek responses
const getRandomGreekResponse = () => {
  const responses = [
    'Καλησπέρα! Πώς μπορώ να σας βοηθήσω σήμερα;',
    'Χαίρομαι που επικοινωνήσατε μαζί μας. Πώς μπορώ να σας εξυπηρετήσω;',
    'Είμαι εδώ για να απαντήσω στις ερωτήσεις σας. Πώς μπορώ να βοηθήσω;',
    'Ευχαριστώ για το μήνυμά σας. Τι θα θέλατε να μάθετε;',
    'Καλώς ήρθατε στην υπηρεσία εξυπηρέτησης. Πώς μπορώ να σας βοηθήσω σήμερα;',
    'Είμαι ο ψηφιακός σας βοηθός. Πείτε μου πώς μπορώ να σας εξυπηρετήσω.',
    'Χαίρομαι που συνομιλούμε! Πώς μπορώ να σας βοηθήσω;',
    'Είμαι στη διάθεσή σας για οποιαδήποτε ερώτηση ή απορία έχετε.',
    'Ευχαριστώ που επικοινωνήσατε μαζί μας. Πώς μπορώ να σας βοηθήσω σήμερα;',
    'Καλώς ήρθατε! Είμαι εδώ για να απαντήσω στις ερωτήσεις σας.'
  ];
  
  return responses[Math.floor(Math.random() * responses.length)];
};

// Helper function to generate contextual responses based on user input
const generateContextualResponse = (userMessage) => {
  // Convert to lowercase for easier matching
  const message = userMessage.toLowerCase();
  
  // Check for common keywords and return appropriate responses
  if (message.includes('γεια') || message.includes('καλησπέρα') || message.includes('καλημέρα')) {
    return 'Γεια σας! Πώς μπορώ να σας βοηθήσω σήμερα;';
  }
  
  if (message.includes('ευχαριστώ') || message.includes('ευχαριστουμε')) {
    return 'Παρακαλώ! Είμαι πάντα στη διάθεσή σας για οποιαδήποτε βοήθεια χρειαστείτε.';
  }
  
  if (message.includes('τράπεζα') || message.includes('λογαριασμό') || message.includes('χρήματα')) {
    return 'Για θέματα που αφορούν τραπεζικές συναλλαγές και λογαριασμούς, μπορείτε να επισκεφθείτε το πλησιέστερο κατάστημα ή να χρησιμοποιήσετε την υπηρεσία e-banking. Μπορώ να σας δώσω περισσότερες πληροφορίες αν χρειάζεστε.';
  }
  
  if (message.includes('κάρτα') || message.includes('πιστωτική') || message.includes('χρεωστική')) {
    return 'Διαθέτουμε διάφορους τύπους καρτών για να καλύψουμε τις ανάγκες σας. Θα θέλατε να μάθετε περισσότερα για τις πιστωτικές ή τις χρεωστικές κάρτες μας;';
  }
  
  if (message.includes('δάνειο') || message.includes('στεγαστικό') || message.includes('καταναλωτικό')) {
    return 'Προσφέρουμε ανταγωνιστικά επιτόκια για όλα τα είδη δανείων. Θα θέλατε να σας ενημερώσω για τα τρέχοντα προγράμματα δανειοδότησης;';
  }
  
  // Default response if no keywords match
  return getRandomGreekResponse();
};

// Create a new thread
router.post('/', (req, res) => {
  const threadId = uuidv4();
  threads[threadId] = {
    id: threadId,
    messages: [],
    createdAt: new Date().toISOString()
  };
  
  res.status(201).json({
    payload: {
      threadId: threadId
    },
    exception: null,
    messages: null,
    executionTime: getRandomResponseTime()
  });
});

// Send message to thread and wait for response
router.post('/:threadId/runs/wait', (req, res) => {
  const { threadId } = req.params;
  const { payload } = req.body;
  
  // Check if thread exists
  if (!threads[threadId]) {
    // Create thread if it doesn't exist
    threads[threadId] = {
      id: threadId,
      messages: [],
      createdAt: new Date().toISOString()
    };
  }
  
  // Add user message to thread
  if (payload && payload.UserMessage) {
    threads[threadId].messages.push({
      role: 'user',
      content: payload.UserMessage,
      timestamp: new Date().toISOString()
    });
    
    // Generate agent response
    const agentMessage = generateContextualResponse(payload.UserMessage);
    
    // Add agent message to thread
    threads[threadId].messages.push({
      role: 'assistant',
      content: agentMessage,
      timestamp: new Date().toISOString()
    });
    
    // Simulate processing time
    setTimeout(() => {
      res.status(200).json({
        payload: {
          agentMessage: agentMessage,
          threadId: threadId
        },
        exception: null,
        messages: null,
        executionTime: getRandomResponseTime()
      });
    }, getRandomResponseTime());
  } else {
    res.status(400).json({
      payload: null,
      exception: {
        message: 'Invalid request: UserMessage is required'
      },
      messages: null,
      executionTime: 0
    });
  }
});

// Get thread details
router.get('/:threadId', (req, res) => {
  const { threadId } = req.params;
  
  if (threads[threadId]) {
    res.status(200).json({
      payload: threads[threadId],
      exception: null,
      messages: null,
      executionTime: getRandomResponseTime()
    });
  } else {
    res.status(404).json({
      payload: null,
      exception: {
        message: 'Thread not found'
      },
      messages: null,
      executionTime: 0
    });
  }
});

// Submit feedback for a thread
router.post('/:threadId/feedback', (req, res) => {
  const { threadId } = req.params;
  const { payload } = req.body;
  
  if (!threads[threadId]) {
    return res.status(404).json({
      payload: null,
      exception: {
        message: 'Thread not found'
      },
      messages: null,
      executionTime: 0
    });
  }
  
  if (!payload || typeof payload.rating !== 'number' || payload.rating < 1 || payload.rating > 5) {
    return res.status(400).json({
      payload: null,
      exception: {
        message: 'Invalid feedback: rating must be a number between 1 and 5'
      },
      messages: null,
      executionTime: 0
    });
  }
  
  // Store feedback
  feedback[threadId] = {
    threadId,
    rating: payload.rating,
    comment: payload.comment || '',
    timestamp: new Date().toISOString()
  };
  
  console.log(`Received feedback for thread ${threadId}:`, feedback[threadId]);
  
  res.status(200).json({
    payload: {
      success: true,
      message: 'Feedback submitted successfully'
    },
    exception: null,
    messages: null,
    executionTime: getRandomResponseTime()
  });
});

// Get all feedback
router.get('/feedback', (req, res) => {
  res.status(200).json({
    payload: feedback,
    exception: null,
    messages: null,
    executionTime: getRandomResponseTime()
  });
});

module.exports = router;
