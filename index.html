<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chat Bubble Standalone - Self-Contained</title>
  </head>
  <body>
    <div class="container">
      <h1>Chat Bubble Widget Demo</h1>
      <p>This is a demonstration of the self-contained chat bubble widget that can be embedded in any website with just two files.</p>
      <p><strong>Dependencies:</strong> Only requires the UMD bundle and loader script - no external CDN dependencies!</p>

      <!-- The chat bubble will be mounted to this div -->
      <!-- You can configure the API URL using data attributes -->
      <div id="chat-bubble-root" data-api-url="/api/cc-lg"></div>

      <!-- Configuration section -->
      <div class="config-section">
        <h2>Widget Configuration</h2>
        <div class="config-form">
          <div class="form-group">
            <label for="api-url">API URL:</label>
            <input type="text" id="api-url" value="/api/cc-lg" />
          </div>
          <button id="apply-config">Apply Configuration</button>
        </div>
      </div>
    </div>

    <!-- Load the self-contained chat bubble widget -->
    <!-- Only these two files are needed - no external dependencies! -->
    <script src="./dist/chat-bubble-widget.umd.cjs"></script>
    <script src="./dist/chat-bubble-loader.js"></script>
    <!-- reCAPTCHA is loaded dynamically only when needed -->

    <!-- Initialize the chat bubble -->
    <script>
      // The widget auto-mounts to #chat-bubble-root with data-api-url configuration
      // But we can also manually control it for the demo configuration section

      document.addEventListener('DOMContentLoaded', function() {
        // Get the API URL from the input field
        const apiUrlInput = document.getElementById('api-url');
        const applyButton = document.getElementById('apply-config');

        // Apply button click handler
        applyButton.addEventListener('click', function() {
          const apiUrl = apiUrlInput.value.trim();
          console.log('Updating chat bubble with API URL:', apiUrl);

          // Clear the existing chat bubble
          const rootElement = document.getElementById('chat-bubble-root');
          rootElement.innerHTML = '';

          // Update the data attribute
          rootElement.setAttribute('data-api-url', apiUrl);

          // Mount a new chat bubble with the updated configuration
          if (window.ChatBubbleLoader) {
            window.ChatBubbleLoader.mount('chat-bubble-root', {
              apiUrl: apiUrl
            });
          } else {
            console.error('ChatBubbleLoader not available');
          }
        });
      });
    </script>

    <!-- Simple styles for the demo page -->
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f7fa;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      h1 {
        color: #0b2d7a;
      }
      .config-section {
        margin-top: 30px;
        padding: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input[type="text"] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
      }
      button {
        background-color: #0b2d7a;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
      }
      button:hover {
        background-color: #0a1f4d;
      }
    </style>
  </body>
</html>
