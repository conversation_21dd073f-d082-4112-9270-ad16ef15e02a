<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    <title>Chat Bubble Standalone</title>
  </head>
  <body>
    <div class="container">
      <h1>Chat Bubble Widget Demo</h1>
      <p>This is a demonstration of the chat bubble widget that can be embedded in any website.</p>

      <!-- The chat bubble will be mounted to this div -->
      <div id="chat-bubble-root"></div>

      <!-- Configuration section -->
      <div class="config-section">
        <h2>Widget Configuration</h2>
        <div class="config-form">
          <div class="form-group">
            <label for="api-url">API URL:</label>
            <input type="text" id="api-url" value="/api/cc-lg" />
          </div>
          <button id="apply-config">Apply Configuration</button>
        </div>
      </div>
    </div>

    <!-- Load the chat bubble widget script -->
    <script src="dist/chat-bubble-widget.umd.cjs"></script>
    <!-- Google reCAPTCHA v2 -->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>

    <!-- Initialize the chat bubble -->
    <script>
      // Function to check if the widget is loaded
      function isWidgetLoaded() {
        return window.ChatBubbleWidget && typeof window.ChatBubbleWidget.mount === 'function';
      }

      // Function to wait for the widget to load
      function waitForWidget(callback, maxAttempts = 20, interval = 100) {
        let attempts = 0;

        const checkWidget = function() {
          if (isWidgetLoaded()) {
            callback();
          } else if (attempts < maxAttempts) {
            attempts++;
            setTimeout(checkWidget, interval);
          } else {
            console.error('ChatBubbleWidget failed to load after multiple attempts');
          }
        };

        checkWidget();
      }

      document.addEventListener('DOMContentLoaded', function() {
        // Get the API URL from the input field
        const apiUrlInput = document.getElementById('api-url');
        const applyButton = document.getElementById('apply-config');

        // Function to mount the chat bubble with the current configuration
        function mountChatBubble() {
          const apiUrl = apiUrlInput.value.trim();
          console.log('Mounting chat bubble with API URL:', apiUrl);

          // Wait for the widget to load before mounting
          waitForWidget(function() {
            window.ChatBubbleWidget.mount('chat-bubble-root', {
              apiUrl: apiUrl
            });
            console.log('Chat bubble mounted successfully');
          });
        }

        // Mount the chat bubble initially
        mountChatBubble();

        // Apply button click handler
        applyButton.addEventListener('click', function() {
          // Clear the existing chat bubble
          const rootElement = document.getElementById('chat-bubble-root');
          rootElement.innerHTML = '';

          // Mount a new chat bubble with the updated configuration
          mountChatBubble();
        });
      });
    </script>

    <!-- Simple styles for the demo page -->
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f7fa;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      h1 {
        color: #0b2d7a;
      }
      .config-section {
        margin-top: 30px;
        padding: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input[type="text"] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
      }
      button {
        background-color: #0b2d7a;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
      }
      button:hover {
        background-color: #0a1f4d;
      }
    </style>
  </body>
</html>
